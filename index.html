<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> - Profile</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation bar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <span>AJ</span>
            </div>
            <ul class="nav-menu">
                <li><a href="#profile" class="nav-link">Profile</a></li>
                <li><a href="#about" class="nav-link">About</a></li>
                <li><a href="#skills" class="nav-link">Skills</a></li>
                <li><a href="#contact" class="nav-link">Contact</a></li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Profile Header Section -->
    <section id="profile" class="profile-header">
        <div class="container">
            <div class="profile-content">
                <div class="profile-image">
                    <img src="pic/picture.jpg" alt="Alex Johnson" id="profilePic">
                    <div class="status-indicator online"></div>
                </div>
                <div class="profile-info">
                    <h1 class="profile-name" id="profileName">Alex Johnson</h1>
                    <p class="profile-title" id="profileTitle">Full Stack Developer</p>
                    <p class="profile-location">
                        <i class="fas fa-map-marker-alt"></i>
                        <span id="profileLocation">San Francisco, CA</span>
                    </p>
                    <div class="profile-stats">
                        <div class="stat">
                            <span class="stat-number">5+</span>
                            <span class="stat-label">Years Experience</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">50+</span>
                            <span class="stat-label">Projects Completed</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">15+</span>
                            <span class="stat-label">Technologies</span>
                        </div>
                    </div>
                    <button type="button" class="edit-profile-btn" id="editProfileBtn">
                        <i class="fas fa-edit"></i> Edit Profile
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about-section">
        <div class="container">
            <h2 class="section-title">About Me</h2>
            <div class="about-content">
                <div class="bio">
                    <p id="profileBio">
                        Passionate full-stack developer with 5+ years of experience creating innovative web applications.
                        I specialize in modern JavaScript frameworks, cloud technologies, and user-centered design.
                        Always eager to learn new technologies and solve complex problems.
                    </p>
                    <div class="interests">
                        <h3>Interests</h3>
                        <div class="interest-tags" id="interestTags">
                            <span class="tag">Web Development</span>
                            <span class="tag">Machine Learning</span>
                            <span class="tag">Photography</span>
                            <span class="tag">Travel</span>
                            <span class="tag">Open Source</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="skills-section">
        <div class="container">
            <h2 class="section-title">Skills & Technologies</h2>
            <div class="skills-grid">
                <div class="skill-category">
                    <h3>Frontend</h3>
                    <div class="skill-items">
                        <div class="skill-item">
                            <span class="skill-name">JavaScript</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="90%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">React</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="85%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">CSS/SCSS</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="88%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="skill-category">
                    <h3>Backend</h3>
                    <div class="skill-items">
                        <div class="skill-item">
                            <span class="skill-name">Node.js</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="82%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">Python</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="78%"></div>
                            </div>
                        </div>
                        <div class="skill-item">
                            <span class="skill-name">MongoDB</span>
                            <div class="skill-bar">
                                <div class="skill-progress" data-width="75%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section">
        <div class="container">
            <h2 class="section-title">Get In Touch</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <div class="contact-details">
                            <h4>Email</h4>
                            <p id="contactEmail"><EMAIL></p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <div class="contact-details">
                            <h4>Phone</h4>
                            <p id="contactPhone">+****************</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <i class="fab fa-linkedin"></i>
                        <div class="contact-details">
                            <h4>LinkedIn</h4>
                            <p>linkedin.com/in/alexjohnson</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <i class="fab fa-github"></i>
                        <div class="contact-details">
                            <h4>GitHub</h4>
                            <p>github.com/alexjohnson</p>
                        </div>
                    </div>
                </div>
                <div class="contact-form">
                    <form id="contactForm">
                        <div class="form-group">
                            <input type="text" id="senderName" placeholder="Your Name" required>
                        </div>
                        <div class="form-group">
                            <input type="email" id="senderEmail" placeholder="Your Email" required>
                        </div>
                        <div class="form-group">
                            <textarea id="message" placeholder="Your Message" rows="5" required></textarea>
                        </div>
                        <button type="submit" class="submit-btn">
                            <i class="fas fa-paper-plane"></i>
                            Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Edit Profile Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Edit Profile</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <div class="form-group">
                        <label for="editName">Name:</label>
                        <input type="text" id="editName" required>
                    </div>
                    <div class="form-group">
                        <label for="editTitle">Title:</label>
                        <input type="text" id="editTitle" required>
                    </div>
                    <div class="form-group">
                        <label for="editLocation">Location:</label>
                        <input type="text" id="editLocation" required>
                    </div>
                    <div class="form-group">
                        <label for="editBio">Bio:</label>
                        <textarea id="editBio" rows="4" required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="editEmail">Email:</label>
                        <input type="email" id="editEmail" required>
                    </div>
                    <div class="form-group">
                        <label for="editPhone">Phone:</label>
                        <input type="tel" id="editPhone" required>
                    </div>
                    <div class="modal-actions">
                        <button type="button" class="cancel-btn">Cancel</button>
                        <button type="submit" class="save-btn">Save Changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="main.js"></script>
</body>
</html>
